import { useState } from 'react';
import { Edit, Trash2, Eye } from 'lucide-react';
import { galleryCategories } from '../../data/galleryImages';

const GalleryGrid = ({ 
  data = [], 
  onEdit, 
  onDelete, 
  onView 
}) => {
  const [imagesLoaded, setImagesLoaded] = useState({});

  const handleImageLoad = (imageId) => {
    setImagesLoaded(prev => ({ ...prev, [imageId]: true }));
  };

  const getSizeClasses = (size) => {
    switch (size) {
      case 'large':
        return 'md:col-span-2 md:row-span-2 h-64 md:h-96';
      case 'wide':
        return 'md:col-span-2 h-48 md:h-64';
      case 'tall':
        return 'md:row-span-2 h-80 md:h-96';
      case 'square':
        return 'aspect-square h-64';
      default:
        return 'h-64';
    }
  };

  const getCategoryData = (categoryId) => {
    return galleryCategories.find(cat => cat.id === categoryId);
  };

  if (data.length === 0) {
    return (
      <div className="bg-white brutal-border brutal-shadow p-8 text-center">
        <div className="bg-gray-100 brutal-border brutal-shadow p-6">
          <h3 className="brutal-text text-lg mb-2">NO IMAGES FOUND</h3>
          <p className="font-bold text-gray-600">
            No gallery images available. Add some images to get started.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white brutal-border brutal-shadow p-6">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 auto-rows-max">
        {data.map((image, index) => {
          const categoryData = getCategoryData(image.category);
          
          return (
            <div
              key={image.id}
              className={`group relative ${getSizeClasses(image.size)} ${
                index % 3 === 0 ? 'asymmetric-grid' : index % 3 === 1 ? 'anti-asymmetric' : ''
              }`}
            >
              <div className="h-full bg-white brutal-border brutal-shadow overflow-hidden group-hover:translate-x-2 group-hover:translate-y-2 group-hover:shadow-none transition-all duration-300">
                {/* Image Container */}
                <div className="relative overflow-hidden flex-1 h-3/4">
                  {/* Loading skeleton */}
                  {!imagesLoaded[image.id] && (
                    <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
                      <div className="brutal-text text-gray-400 text-sm">LOADING...</div>
                    </div>
                  )}
                  
                  <img
                    src={image.imageUrl || image.src}
                    alt={image.alt || image.title}
                    className={`w-full h-full object-cover transition-all duration-300 group-hover:scale-105 ${
                      imagesLoaded[image.id] ? 'opacity-100' : 'opacity-0'
                    }`}
                    onLoad={() => handleImageLoad(image.id)}
                    loading="lazy"
                  />
                  
                  {/* Overlay with actions */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-70 transition-all duration-300 flex items-center justify-center">
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex gap-2">
                      {onView && (
                        <button
                          onClick={() => onView(image)}
                          className="p-2 bg-blue-500 text-white brutal-border brutal-shadow-small hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
                          title="View"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                      )}
                      {onEdit && (
                        <button
                          onClick={() => onEdit(image)}
                          className="p-2 bg-yellow-400 text-black brutal-border brutal-shadow-small hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
                          title="Edit"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                      )}
                      {onDelete && (
                        <button
                          onClick={() => onDelete(image)}
                          className="p-2 bg-red-500 text-white brutal-border brutal-shadow-small hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
                          title="Delete"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  </div>
                </div>
                
                {/* Image Info */}
                <div className="p-4 h-1/4 flex flex-col justify-between">
                  <div>
                    <h3 className="brutal-text text-sm lg:text-base mb-1 line-clamp-1">{image.title}</h3>
                    <p className="font-bold text-xs text-gray-600 mb-2 line-clamp-2">
                      {image.description || image.alt}
                    </p>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    {categoryData && (
                      <span className={`px-2 py-1 text-xs font-bold brutal-border ${categoryData.color} text-white`}>
                        {categoryData.name}
                      </span>
                    )}
                    <span className="text-xs font-bold text-gray-500 uppercase">
                      {image.size}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default GalleryGrid;
