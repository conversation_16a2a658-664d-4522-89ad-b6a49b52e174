import React from 'react';
import { Link } from 'react-router-dom';
import { 
  Image, 
  MessageSquare, 
  FileText, 
  Download, 
  BookOpen, 
  Star,
  TrendingUp,
  Users,
  Eye,
  Plus
} from 'lucide-react';
import StatsCard from '../../components/admin/StatsCard';
import ActionButton from '../../components/admin/ActionButton';

import { useRealtimeCollection } from '../../hooks/useFirestore';
import { COLLECTIONS } from '../../services/firebase';

const AdminDashboard = () => {
  // Real-time data from Firebase
  const { documents: galleryImages } = useRealtimeCollection(COLLECTIONS.GALLERY_IMAGES);
  const { documents: blogPosts } = useRealtimeCollection(COLLECTIONS.BLOG_POSTS);
  const { documents: caseStudies } = useRealtimeCollection(COLLECTIONS.CASE_STUDIES);
  const { documents: testimonials } = useRealtimeCollection(COLLECTIONS.TESTIMONIALS);
  const { documents: resources } = useRealtimeCollection(COLLECTIONS.RESOURCES);
  const { documents: contactMessages } = useRealtimeCollection(COLLECTIONS.CONTACT_MESSAGES);
  
  // Calculate stats
  const stats = [
    {
      title: 'Gallery Images',
      value: galleryImages?.length || 0,
      icon: Image,
      color: 'blue',
      trend: 'up',
      trendValue: '+12%'
    },
    {
      title: 'Contact Messages',
      value: contactMessages?.length || 0,
      icon: MessageSquare,
      color: 'yellow',
      trend: 'up',
      trendValue: '+5 new'
    },
    {
      title: 'Case Studies',
      value: caseStudies?.length || 0,
      icon: FileText,
      color: 'green',
      trend: 'neutral',
      trendValue: 'No change'
    },
    {
      title: 'Resources',
      value: resources?.length || 0,
      icon: Download,
      color: 'purple',
      trend: 'up',
      trendValue: '+2 new'
    },
    {
      title: 'Blog Posts',
      value: blogPosts?.length || 0,
      icon: BookOpen,
      color: 'pink',
      trend: 'up',
      trendValue: '+1 new'
    },
    {
      title: 'Testimonials',
      value: testimonials?.length || 0,
      icon: Star,
      color: 'red',
      trend: 'up',
      trendValue: '+3 new'
    }
  ];

  const quickActions = [
    { title: 'Add Gallery Image', href: '/admin/gallery', icon: Image, color: 'blue' },
    { title: 'View Messages', href: '/admin/contacts', icon: MessageSquare, color: 'yellow' },
    { title: 'Create Case Study', href: '/admin/case-studies', icon: FileText, color: 'green' },
    { title: 'Add Resource', href: '/admin/resources', icon: Download, color: 'purple' },
    { title: 'Write Blog Post', href: '/admin/blog', icon: BookOpen, color: 'pink' },
    { title: 'Add Testimonial', href: '/admin/testimonials', icon: Star, color: 'red' }
  ];

  const recentActivity = [
    { action: 'New contact message received', time: '2 minutes ago', type: 'message' },
    { action: 'Gallery image uploaded', time: '1 hour ago', type: 'gallery' },
    { action: 'Blog post published', time: '3 hours ago', type: 'blog' },
    { action: 'Testimonial added', time: '5 hours ago', type: 'testimonial' },
    { action: 'Case study updated', time: '1 day ago', type: 'case-study' }
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-white brutal-border brutal-shadow p-4 sm:p-6 asymmetric-grid">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div>
            <div className="bg-primary-blue text-white px-4 py-2 brutal-border brutal-shadow-small inline-block mb-3 transform -rotate-1">
              <h1 className="brutal-text text-xl sm:text-2xl">ADMIN DASHBOARD</h1>
            </div>
            <p className="font-bold text-gray-600 text-sm sm:text-base">
              Welcome back! Here's what's happening with your content.
            </p>
          </div>

          <div className="bg-accent-green text-black px-3 py-2 sm:px-4 brutal-border brutal-shadow-small transform rotate-1 hover:rotate-0 transition-transform duration-150">
            <div className="flex items-center gap-2">
              <TrendingUp className="w-4 h-4 sm:w-5 sm:h-5" />
              <span className="font-bold text-sm sm:text-base">ALL SYSTEMS OPERATIONAL</span>
            </div>
          </div>
        </div>
      </div>



      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {stats.map((stat, index) => (
          <StatsCard
            key={index}
            title={stat.title}
            value={stat.value}
            icon={stat.icon}
            color={stat.color}
            trend={stat.trend}
            trendValue={stat.trendValue}
          />
        ))}
      </div>

      {/* Quick Actions */}
      <div className="bg-white brutal-border brutal-shadow p-4 sm:p-6 anti-asymmetric">
        <div className="bg-primary-pink text-white px-4 py-2 brutal-border brutal-shadow-small inline-block mb-4 sm:mb-6 transform rotate-1">
          <h2 className="brutal-text text-lg sm:text-xl">QUICK ACTIONS</h2>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {quickActions.map((action, index) => {
            const Icon = action.icon;
            const colorClasses = {
              blue: 'bg-primary-blue text-white',
              yellow: 'bg-accent-yellow text-black',
              green: 'bg-accent-green text-black',
              purple: 'bg-purple-500 text-white',
              pink: 'bg-primary-pink text-white',
              red: 'bg-red-500 text-white'
            };
            
            return (
              <Link
                key={index}
                to={action.href}
                className={`${colorClasses[action.color]} p-3 sm:p-4 brutal-border brutal-shadow hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150 flex items-center gap-2 sm:gap-3 ${index % 2 === 0 ? 'asymmetric-grid' : 'anti-asymmetric'}`}
              >
                <Icon className="w-5 h-5 sm:w-6 sm:h-6" />
                <span className="font-bold text-sm sm:text-base">{action.title}</span>
              </Link>
            );
          })}
        </div>
      </div>

      {/* Recent Activity & Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <div className="bg-white brutal-border brutal-shadow p-4 sm:p-6 asymmetric-grid">
          <div className="bg-accent-yellow text-black px-4 py-2 brutal-border brutal-shadow-small inline-block mb-4 sm:mb-6 transform -rotate-1">
            <h2 className="brutal-text text-lg sm:text-xl">RECENT ACTIVITY</h2>
          </div>
          <div className="space-y-4">
            {recentActivity.map((activity, index) => (
              <div key={index} className="flex items-center gap-4 p-3 bg-gray-50 brutal-border">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <div className="flex-1">
                  <p className="font-bold text-sm">{activity.action}</p>
                  <p className="text-xs text-gray-600">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-6">
            <ActionButton variant="outline" size="small">
              <Eye className="w-4 h-4" />
              VIEW ALL ACTIVITY
            </ActionButton>
          </div>
        </div>

        {/* Content Overview */}
        <div className="bg-white brutal-border brutal-shadow p-4 sm:p-6 anti-asymmetric">
          <div className="bg-black text-white px-4 py-2 brutal-border brutal-shadow-small inline-block mb-4 sm:mb-6 transform rotate-1">
            <h2 className="brutal-text text-lg sm:text-xl">CONTENT OVERVIEW</h2>
          </div>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-blue-50 brutal-border">
              <div className="flex items-center gap-3">
                <Image className="w-5 h-5 text-blue-500" />
                <span className="font-bold">Gallery</span>
              </div>
              <span className="brutal-text text-blue-500">{galleryImages.length}</span>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-yellow-50 brutal-border">
              <div className="flex items-center gap-3">
                <MessageSquare className="w-5 h-5 text-yellow-600" />
                <span className="font-bold">Messages</span>
              </div>
              <span className="brutal-text text-yellow-600">{contactMessages?.length || 0}</span>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-green-50 brutal-border">
              <div className="flex items-center gap-3">
                <FileText className="w-5 h-5 text-green-600" />
                <span className="font-bold">Case Studies</span>
              </div>
              <span className="brutal-text text-green-600">{caseStudies.length}</span>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-pink-50 brutal-border">
              <div className="flex items-center gap-3">
                <Star className="w-5 h-5 text-pink-600" />
                <span className="font-bold">Testimonials</span>
              </div>
              <span className="brutal-text text-pink-600">{testimonials.length}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
